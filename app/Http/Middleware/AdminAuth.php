<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminAuth
{
    public function handle(Request $request, Closure $next)
    {
        // Skip authentication check for login routes
        if ($request->is('admin/login') || $request->is('admin/login/*')) {
            return $next($request);
        }

        if (!Auth::check()) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Unauthenticated'], 401);
            }
            return redirect()->route('admin.login');
        }

        $user = Auth::user();

        if (!$user->is_active || !in_array($user->role, ['admin', 'super_admin'])) {
            Auth::logout();
            return redirect()->route('admin.login')->with('error', 'Access denied.');
        }

        return $next($request);
    }
}
