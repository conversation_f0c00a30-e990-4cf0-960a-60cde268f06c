<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    /**
     * Display a listing of contacts for admin
     */
    public function index(Request $request): JsonResponse
    {
        $query = Contact::query();

        // Apply filters
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        if ($request->has('status')) {
            $query->byStatus($request->get('status'));
        }

        if ($request->has('event_type')) {
            $query->where('event_type', $request->get('event_type'));
        }

        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }

        $contacts = $query->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $contacts
        ]);
    }

    /**
     * Store a newly created contact (from website form)
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'event_type' => 'required|string|max:255',
            'message' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['status'] = 'new';

        $contact = Contact::create($data);

        // TODO: Send notification email to admin

        return response()->json([
            'success' => true,
            'message' => 'Thank you for your message! We will contact you soon.',
            'data' => $contact
        ], 201);
    }

    /**
     * Display the specified contact
     */
    public function show(Contact $contact): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $contact
        ]);
    }

    /**
     * Update the specified contact (admin notes)
     */
    public function update(Request $request, Contact $contact): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'admin_notes' => 'nullable|string',
            'status' => 'nullable|in:new,contacted,closed'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        // Set contacted_at timestamp if status is being changed to contacted
        if (isset($data['status']) && $data['status'] === 'contacted' && $contact->status !== 'contacted') {
            $data['contacted_at'] = now();
        }

        $contact->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Contact updated successfully',
            'data' => $contact
        ]);
    }

    /**
     * Remove the specified contact
     */
    public function destroy(Contact $contact): JsonResponse
    {
        $contact->delete();

        return response()->json([
            'success' => true,
            'message' => 'Contact deleted successfully'
        ]);
    }

    /**
     * Update contact status
     */
    public function updateStatus(Request $request, Contact $contact): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:new,contacted,closed'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $status = $request->get('status');
        $updateData = ['status' => $status];

        // Set contacted_at timestamp if status is being changed to contacted
        if ($status === 'contacted' && $contact->status !== 'contacted') {
            $updateData['contacted_at'] = now();
        }

        $contact->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Contact status updated successfully',
            'data' => $contact
        ]);
    }

    /**
     * Mark all new contacts as contacted
     */
    public function markAllAsRead(): JsonResponse
    {
        $updatedCount = Contact::where('status', 'new')->update([
            'status' => 'contacted',
            'contacted_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => "Marked {$updatedCount} contacts as contacted",
            'updated_count' => $updatedCount
        ]);
    }
}
