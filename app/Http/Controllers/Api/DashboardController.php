<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Event;
use Illuminate\Http\JsonResponse;

class DashboardController extends Controller
{
    /**
     * Get dashboard data
     */
    public function index(): JsonResponse
    {
        // Get statistics
        $stats = [
            'totalEvents' => Event::count(),
            'activeServices' => 8, // Mock data - replace with actual services count
            'newContacts' => 5, // Mock data - replace with actual contacts count
            'featuredEvents' => Event::where('is_featured', true)->count(),
        ];

        // Get recent events (last 5)
        $recentEvents = Event::orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Mock recent contacts - replace with actual contacts model
        $recentContacts = [
            [
                'id' => 1,
                'name' => '<PERSON>',
                'event_type' => 'Wedding',
                'status' => 'new'
            ],
            [
                'id' => 2,
                'name' => '<PERSON>',
                'event_type' => 'Corporate Event',
                'status' => 'contacted'
            ],
            [
                'id' => 3,
                'name' => '<PERSON>',
                'event_type' => 'Birthday Party',
                'status' => 'new'
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'stats' => $stats,
                'recentEvents' => $recentEvents,
                'recentContacts' => $recentContacts
            ]
        ]);
    }
}
