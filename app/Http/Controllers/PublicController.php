<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Models\Event;
use Illuminate\Http\Request;

class PublicController extends Controller
{
    public function home()
    {
        // Get featured upcoming events for the homepage
        $featuredEvents = Event::active()
            ->featured()
            ->upcoming()
            ->orderBy('event_date', 'asc')
            ->take(6)
            ->get();

        return view('public.home', compact('featuredEvents'));
    }

    public function storeContact(Request $request)
    {
        // Handle contact form submission
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'event_type' => 'required|string',
            'message' => 'required|string'
        ]);

        // Store contact (you'll need to create Contact model)
         Contact::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Thank you for your message! We will contact you soon.'
        ]);
    }
}
