{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^2.0.0", "tailwindcss": "^4.0.0", "vite": "^7.0.4"}, "dependencies": {"@vitejs/plugin-vue": "^6.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.535.0", "radix-vue": "^1.9.17", "tailwind-merge": "^3.3.1", "vue": "^3.5.18", "vue-router": "^4.5.1"}}