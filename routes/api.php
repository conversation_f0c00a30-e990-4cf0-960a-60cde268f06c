<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\EventController;
use App\Http\Controllers\Api\ServiceController;
use App\Http\Controllers\Api\ContactController;
use App\Http\Controllers\Api\SettingController;
use App\Http\Controllers\Api\DashboardController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Admin API routes
Route::prefix('admin')->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index']);

    // Events
    Route::get('/events', [EventController::class, 'index']);
    Route::post('/events', [EventController::class, 'store']);
    Route::get('/events/{event}', [EventController::class, 'show']);
    Route::put('/events/{event}', [EventController::class, 'update']);
    Route::delete('/events/{event}', [EventController::class, 'destroy']);
    Route::patch('/events/{event}/toggle-status', [EventController::class, 'toggleStatus']);
    Route::patch('/events/{event}/toggle-featured', [EventController::class, 'toggleFeatured']);

    // Services
    Route::get('/services', [ServiceController::class, 'index']);
    Route::post('/services', [ServiceController::class, 'store']);
    Route::get('/services/{service}', [ServiceController::class, 'show']);
    Route::put('/services/{service}', [ServiceController::class, 'update']);
    Route::delete('/services/{service}', [ServiceController::class, 'destroy']);
    Route::patch('/services/{service}/toggle-status', [ServiceController::class, 'toggleStatus']);

    // Contacts
    Route::get('/contacts', [ContactController::class, 'index']);
    Route::get('/contacts/{contact}', [ContactController::class, 'show']);
    Route::patch('/contacts/{contact}/status', [ContactController::class, 'updateStatus']);
    Route::post('/contacts/mark-all-read', [ContactController::class, 'markAllAsRead']);
    Route::delete('/contacts/{contact}', [ContactController::class, 'destroy']);

    // Settings
    Route::get('/settings', [SettingController::class, 'index']);
    Route::put('/settings', [SettingController::class, 'update']);
});

// Public API routes
Route::get('/events', [EventController::class, 'publicIndex']);
Route::get('/events/{event}', [EventController::class, 'show']);
Route::get('/services', [ServiceController::class, 'publicIndex']);
Route::post('/contact', [ContactController::class, 'store']);
Route::get('/settings/public', [SettingController::class, 'publicSettings']);
