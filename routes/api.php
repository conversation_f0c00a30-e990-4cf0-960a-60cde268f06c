<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\EventController;
use App\Http\Controllers\Api\DashboardController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Admin API routes
Route::prefix('admin')->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index']);

    // Events
    Route::get('/events', [EventController::class, 'index']);
    Route::post('/events', [EventController::class, 'store']);
    Route::get('/events/{event}', [EventController::class, 'show']);
    Route::put('/events/{event}', [EventController::class, 'update']);
    Route::delete('/events/{event}', [EventController::class, 'destroy']);
    Route::patch('/events/{event}/toggle-status', [EventController::class, 'toggleStatus']);
    Route::patch('/events/{event}/toggle-featured', [EventController::class, 'toggleFeatured']);
});

// Public API routes
Route::get('/events', [EventController::class, 'publicIndex']);
Route::get('/events/{event}', [EventController::class, 'show']);
