<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PublicController;
use App\Http\Controllers\Auth\AdminAuthController;

// Public website routes
Route::get('/', [PublicController::class, 'home'])->name('public.home');
Route::post('/contact', [PublicController::class, 'storeContact'])->name('public.contact.store');

// Admin authentication routes (outside middleware)
Route::get('/admin/login', [AdminAuthController::class, 'showLoginForm'])->name('admin.login');
Route::post('/admin/login', [AdminAuthController::class, 'login']);
Route::post('/admin/logout', [AdminAuthController::class, 'logout'])->name('admin.logout');

// Admin entry point - redirects to dashboard if authenticated, login if not
Route::get('/admin', function () {
    if (auth()->check() && auth()->user()->is_active && in_array(auth()->user()->role, ['admin', 'super_admin'])) {
        return redirect('/admin/dashboard');
    }
    return redirect()->route('admin.login');
})->name('admin');

// Protected admin routes
Route::middleware('admin.auth')->prefix('admin')->group(function () {
    Route::get('/{any?}', function () {
        return view('app');
    })->where('any', '.*')->name('admin.dashboard');
});
