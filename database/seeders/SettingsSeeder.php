<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'business_name',
                'value' => 'The Grill Gather',
                'type' => 'text',
                'group' => 'general',
                'description' => 'Business name displayed on the website'
            ],
            [
                'key' => 'tagline',
                'value' => 'a Taste of Togetherness',
                'type' => 'text',
                'group' => 'general',
                'description' => 'Business tagline'
            ],
            [
                'key' => 'description',
                'value' => 'Your premier BBQ and catering service. We go where the grill is needed.',
                'type' => 'textarea',
                'group' => 'general',
                'description' => 'Business description'
            ],
            
            // Contact Information
            [
                'key' => 'email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'group' => 'contact',
                'description' => 'Primary contact email'
            ],
            [
                'key' => 'phone_primary',
                'value' => '+256 700 123 456',
                'type' => 'text',
                'group' => 'contact',
                'description' => 'Primary phone number'
            ],
            [
                'key' => 'phone_secondary',
                'value' => '+256 700 123 457',
                'type' => 'text',
                'group' => 'contact',
                'description' => 'Secondary phone number'
            ],
            [
                'key' => 'address',
                'value' => 'Kampala, Uganda',
                'type' => 'textarea',
                'group' => 'contact',
                'description' => 'Business address'
            ],
            
            // Social Media
            [
                'key' => 'facebook_url',
                'value' => '',
                'type' => 'url',
                'group' => 'social',
                'description' => 'Facebook page URL'
            ],
            [
                'key' => 'instagram_url',
                'value' => '',
                'type' => 'url',
                'group' => 'social',
                'description' => 'Instagram profile URL'
            ],
            [
                'key' => 'twitter_url',
                'value' => '',
                'type' => 'url',
                'group' => 'social',
                'description' => 'Twitter profile URL'
            ],
            
            // Business Hours
            [
                'key' => 'business_hours',
                'value' => json_encode([
                    ['name' => 'Monday', 'is_open' => true, 'open_time' => '09:00', 'close_time' => '18:00'],
                    ['name' => 'Tuesday', 'is_open' => true, 'open_time' => '09:00', 'close_time' => '18:00'],
                    ['name' => 'Wednesday', 'is_open' => true, 'open_time' => '09:00', 'close_time' => '18:00'],
                    ['name' => 'Thursday', 'is_open' => true, 'open_time' => '09:00', 'close_time' => '18:00'],
                    ['name' => 'Friday', 'is_open' => true, 'open_time' => '09:00', 'close_time' => '20:00'],
                    ['name' => 'Saturday', 'is_open' => true, 'open_time' => '10:00', 'close_time' => '20:00'],
                    ['name' => 'Sunday', 'is_open' => false, 'open_time' => '10:00', 'close_time' => '18:00']
                ]),
                'type' => 'json',
                'group' => 'general',
                'description' => 'Business operating hours'
            ],
            
            // Email Settings
            [
                'key' => 'smtp_host',
                'value' => '',
                'type' => 'text',
                'group' => 'email',
                'description' => 'SMTP server host'
            ],
            [
                'key' => 'smtp_port',
                'value' => '587',
                'type' => 'number',
                'group' => 'email',
                'description' => 'SMTP server port'
            ],
            [
                'key' => 'smtp_username',
                'value' => '',
                'type' => 'text',
                'group' => 'email',
                'description' => 'SMTP username'
            ],
            [
                'key' => 'smtp_password',
                'value' => '',
                'type' => 'password',
                'group' => 'email',
                'description' => 'SMTP password'
            ],
            
            // SEO Settings
            [
                'key' => 'meta_title',
                'value' => 'The Grill Gather - Premier BBQ & Catering Service',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Website meta title'
            ],
            [
                'key' => 'meta_description',
                'value' => 'Professional BBQ and catering services in Uganda. We bring the grill to you for unforgettable events.',
                'type' => 'textarea',
                'group' => 'seo',
                'description' => 'Website meta description'
            ],
            [
                'key' => 'keywords',
                'value' => 'BBQ, catering, events, Uganda, grill, food service',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Website keywords'
            ],
            
            // System Settings
            [
                'key' => 'maintenance_mode',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'system',
                'description' => 'Enable maintenance mode'
            ]
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
