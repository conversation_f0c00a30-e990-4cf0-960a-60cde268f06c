<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Event;
use Carbon\Carbon;

class EventSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $events = [
            [
                'title' => 'BBQ Party Weekend',
                'description' => 'Join us for the ultimate summer barbecue experience with live music, games, and delicious grilled food. Perfect for families and friends to gather and enjoy great food together.',
                'event_date' => Carbon::now()->addDays(15)->format('Y-m-d'),
                'event_time' => '15:00',
                'location' => 'Valley view Gardens Seeta',
                'features' => ['barbecue', 'live_music', 'games', 'family_friendly'],
                'ticket_prices' => [
                    ['type' => 'Couple', 'price' => 100000],
                    ['type' => 'Singles', 'price' => 60000]
                ],
                'is_featured' => true,
                'is_active' => true,
                'image' => null,
            ],
            [
                'title' => 'Corporate Catering Event',
                'description' => 'Professional catering services for corporate events and meetings. High-quality food and service for your business gatherings.',
                'event_date' => Carbon::now()->addDays(25)->format('Y-m-d'),
                'event_time' => '12:00',
                'location' => 'Downtown Office Complex',
                'features' => ['catering', 'outdoor'],
                'ticket_prices' => [
                    ['type' => 'Standard', 'price' => 50000],
                    ['type' => 'Premium', 'price' => 80000]
                ],
                'is_featured' => false,
                'is_active' => true,
                'image' => null,
            ],
            [
                'title' => 'Family Fun Day',
                'description' => 'A perfect day out for the whole family with games, entertainment, and delicious food. Kids activities and family-friendly atmosphere.',
                'event_date' => Carbon::now()->addDays(35)->format('Y-m-d'),
                'event_time' => '10:00',
                'location' => 'Central Park Kampala',
                'features' => ['family_friendly', 'games', 'barbecue', 'drinks'],
                'ticket_prices' => [
                    ['type' => 'Adult', 'price' => 40000],
                    ['type' => 'Child', 'price' => 20000],
                    ['type' => 'Family Pack', 'price' => 120000]
                ],
                'is_featured' => true,
                'is_active' => true,
                'image' => null,
            ],
            [
                'title' => 'Evening Grill & Chill',
                'description' => 'Relax and unwind with friends at our evening grill session. Great music, cold drinks, and perfectly grilled food.',
                'event_date' => Carbon::now()->addDays(45)->format('Y-m-d'),
                'event_time' => '18:30',
                'location' => 'Lakeside Resort Entebbe',
                'features' => ['barbecue', 'drinks', 'live_music', 'outdoor'],
                'ticket_prices' => [
                    ['type' => 'Regular', 'price' => 75000],
                    ['type' => 'VIP', 'price' => 120000]
                ],
                'is_featured' => false,
                'is_active' => true,
                'image' => null,
            ],
            [
                'title' => 'Wedding Reception Catering',
                'description' => 'Make your special day even more memorable with our premium wedding catering services. Elegant presentation and exceptional taste.',
                'event_date' => Carbon::now()->addDays(60)->format('Y-m-d'),
                'event_time' => '14:00',
                'location' => 'Garden City Wedding Venue',
                'features' => ['catering', 'dancing', 'family_friendly'],
                'ticket_prices' => [
                    ['type' => 'Per Person', 'price' => 85000],
                    ['type' => 'Premium Package', 'price' => 150000]
                ],
                'is_featured' => true,
                'is_active' => true,
                'image' => null,
            ]
        ];

        foreach ($events as $eventData) {
            Event::create($eventData);
        }
    }
}