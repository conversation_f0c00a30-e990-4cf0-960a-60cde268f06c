<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Service;

class ServicesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            // BBQ Specialties
            [
                'name' => "Goat's BBQ",
                'description' => 'Tender, slow-cooked goat meat with our signature BBQ sauce and spices.',
                'category' => 'BBQ Specialties',
                'icon' => 'fas fa-fire',
                'price' => 25000.00,
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'name' => 'BBQ Chicken Bites',
                'description' => 'Juicy chicken pieces marinated and grilled to perfection.',
                'category' => 'BBQ Specialties',
                'icon' => 'fas fa-drumstick-bite',
                'price' => 18000.00,
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'name' => 'Roast Irish Wedges',
                'description' => 'Crispy potato wedges seasoned with herbs and spices.',
                'category' => 'BBQ Specialties',
                'icon' => 'fas fa-seedling',
                'price' => 12000.00,
                'is_active' => true,
                'sort_order' => 3
            ],
            
            // Traditional Favorites
            [
                'name' => 'Steamed Matoke',
                'description' => 'Traditional Ugandan green bananas steamed with aromatic spices.',
                'category' => 'Traditional Favorites',
                'icon' => 'fas fa-leaf',
                'price' => 8000.00,
                'is_active' => true,
                'sort_order' => 4
            ],
            [
                'name' => 'Spiced African Tea',
                'description' => 'Aromatic blend of traditional African spices in hot tea.',
                'category' => 'Traditional Favorites',
                'icon' => 'fas fa-mug-hot',
                'price' => 3000.00,
                'is_active' => true,
                'sort_order' => 5
            ],
            [
                'name' => 'Coffee',
                'description' => 'Fresh brewed Ugandan coffee, rich and aromatic.',
                'category' => 'Traditional Favorites',
                'icon' => 'fas fa-coffee',
                'price' => 4000.00,
                'is_active' => true,
                'sort_order' => 6
            ],
            
            // Additional Items
            [
                'name' => 'Mini Pizzas',
                'description' => 'Individual-sized pizzas with various toppings.',
                'category' => 'Additional Items',
                'icon' => 'fas fa-pizza-slice',
                'price' => 15000.00,
                'is_active' => true,
                'sort_order' => 7
            ],
            [
                'name' => 'Grilled Corn',
                'description' => 'Fresh corn on the cob grilled with butter and spices.',
                'category' => 'Additional Items',
                'icon' => 'fas fa-corn',
                'price' => 5000.00,
                'is_active' => true,
                'sort_order' => 8
            ],
            [
                'name' => 'Fresh Fruit Salad',
                'description' => 'Seasonal fresh fruits mixed with honey and mint.',
                'category' => 'Additional Items',
                'icon' => 'fas fa-apple-alt',
                'price' => 10000.00,
                'is_active' => true,
                'sort_order' => 9
            ],
            
            // Catering Packages
            [
                'name' => 'Birthday Party Package',
                'description' => 'Complete catering package for birthday celebrations including BBQ, sides, and drinks.',
                'category' => 'Catering Packages',
                'icon' => 'fas fa-birthday-cake',
                'price' => 150000.00,
                'is_active' => true,
                'sort_order' => 10
            ],
            [
                'name' => 'Corporate Event Package',
                'description' => 'Professional catering service for corporate events and meetings.',
                'category' => 'Catering Packages',
                'icon' => 'fas fa-briefcase',
                'price' => 200000.00,
                'is_active' => true,
                'sort_order' => 11
            ],
            [
                'name' => 'Wedding Reception Package',
                'description' => 'Elegant catering service for wedding receptions with premium menu options.',
                'category' => 'Catering Packages',
                'icon' => 'fas fa-heart',
                'price' => 300000.00,
                'is_active' => true,
                'sort_order' => 12
            ]
        ];

        foreach ($services as $service) {
            Service::updateOrCreate(
                ['name' => $service['name']],
                $service
            );
        }
    }
}
