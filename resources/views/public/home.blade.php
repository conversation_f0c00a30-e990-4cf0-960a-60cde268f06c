@extends('public.layout')

@section('content')
    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-overlay"></div>
        <div class="hero-content">
            <div class="hero-logo">
                <i class="fas fa-fire flame-icon-large"></i>
                <i class="fas fa-utensils fork-icon"></i>
            </div>
            <h1 class="hero-title">The<span class="grill-highlight">Grill</span> Gather</h1>
            <p class="hero-tagline">a Taste of Togetherness</p>
            <p class="hero-subtitle">"We go where the grill is needed."</p>
            <div class="hero-buttons">
                <a href="#events" class="btn btn-primary">View Events</a>
                <a href="#contact" class="btn btn-secondary">Book Now</a>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services">
        <div class="container">
            <h2 class="section-title">What We Offer</h2>
            <div class="services-grid">
                <div class="service-category">
                    <h3>BBQ Specialties</h3>
                    <ul>
                        <li><i class="fas fa-fire"></i> Goat's BBQ</li>
                        <li><i class="fas fa-drumstick-bite"></i> BBQ Chicken Bites</li>
                        <li><i class="fas fa-seedling"></i> Roast Irish Wedges</li>
                    </ul>
                </div>
                <div class="service-category">
                    <h3>Traditional Favorites</h3>
                    <ul>
                        <li><i class="fas fa-leaf"></i> Steamed Matoke</li>
                        <li><i class="fas fa-mug-hot"></i> Spiced African Tea</li>
                        <li><i class="fas fa-coffee"></i> Coffee</li>
                    </ul>
                </div>
                <div class="service-category">
                    <h3>Additional Items</h3>
                    <ul>
                        <li><i class="fas fa-pizza-slice"></i> Mini Pizzas</li>
                        <li><i class="fas fa-hamburger"></i> Burgers</li>
                        <li><i class="fas fa-apple-alt"></i> Fruits, Juice & Cakes</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Events Section -->
    <section id="events" class="events">
        <h2 class="section-title">Upcoming Events</h2>

        @if($featuredEvents->count() > 0)
            <div class="events-slider-container">
                <div class="events-slider" id="events-slider">
                    @foreach($featuredEvents as $event)
                        <div class="event-card {{ $event->is_featured ? 'featured-event' : '' }}">
                            <div class="event-header">
                                <div class="event-date">
                                    <span class="month">{{ \Carbon\Carbon::parse($event->event_date)->format('M') }}</span>
                                    <span class="day">{{ \Carbon\Carbon::parse($event->event_date)->format('d') }}</span>
                                    <span class="year">{{ \Carbon\Carbon::parse($event->event_date)->format('Y') }}</span>
                                </div>
                                <div class="event-info">
                                    <h3>{{ $event->title }}</h3>
                                    <p class="event-time">
                                        <i class="fas fa-clock"></i>
                                        {{ \Carbon\Carbon::parse($event->event_time)->format('g:i A') }}
                                    </p>
                                    <p class="event-location">
                                        <i class="fas fa-map-marker-alt"></i>
                                        {{ $event->location }}
                                    </p>
                                </div>
                            </div>

                            @if($event->features && count($event->features) > 0)
                                <div class="event-features">
                                    @foreach($event->features as $feature)
                                        @php
                                            $featureIcons = [
                                                'barbecue' => 'fas fa-fire',
                                                'live_music' => 'fas fa-music',
                                                'games' => 'fas fa-gamepad',
                                                'family_friendly' => 'fas fa-users',
                                                'drinks' => 'fas fa-cocktail',
                                                'dancing' => 'fas fa-music',
                                                'outdoor' => 'fas fa-tree',
                                                'catering' => 'fas fa-utensils'
                                            ];
                                            $icon = $featureIcons[$feature] ?? 'fas fa-star';
                                        @endphp
                                        <div class="feature">
                                            <i class="{{ $icon }}"></i>
                                            <span>{{ ucwords(str_replace('_', ' ', $feature)) }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            @endif

                            @if($event->ticket_prices && count($event->ticket_prices) > 0)
                                <div class="event-tickets">
                                    <h4>Tickets</h4>
                                    <div class="ticket-prices">
                                        @foreach($event->ticket_prices as $ticket)
                                            @php
                                                $formattedPrice = $ticket['price'] >= 1000
                                                    ? number_format($ticket['price'] / 1000) . 'K'
                                                    : number_format($ticket['price']);
                                            @endphp
                                            <div class="ticket-type">
                                                <span class="price">{{ $formattedPrice }}</span>
                                                <span class="type">{{ $ticket['type'] }}</span>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>

                <!-- Slider Controls -->
                <div class="slider-controls">
                    <button class="slider-btn" id="prev-btn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="slider-btn" id="next-btn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

                <!-- Slider Indicators -->
                <div class="slider-indicators" id="slider-indicators">
                    <!-- Indicators will be generated by JavaScript -->
                </div>
            </div>
        @else
            <div class="container">
                <div class="no-events">
                    <p>No upcoming events at the moment. Check back soon!</p>
                </div>
            </div>
        @endif
    </section>

    <!-- Event Types Section -->
    <section class="event-types">
        <div class="container">
            <h2 class="section-title">Perfect For Your Special Occasions</h2>
            <div class="event-types-grid">
                <div class="event-type">
                    <i class="fas fa-birthday-cake"></i>
                    <span>Birthday Parties</span>
                </div>
                <div class="event-type">
                    <i class="fas fa-hands-praying"></i>
                    <span>Fellowships</span>
                </div>
                <div class="event-type">
                    <i class="fas fa-campground"></i>
                    <span>Camping</span>
                </div>
                <div class="event-type">
                    <i class="fas fa-users"></i>
                    <span>Meetings</span>
                </div>
                <div class="event-type">
                    <i class="fas fa-mountain"></i>
                    <span>Retreats</span>
                </div>
                <div class="event-type">
                    <i class="fas fa-graduation-cap"></i>
                    <span>Graduation</span>
                </div>
                <div class="event-type">
                    <i class="fas fa-handshake"></i>
                    <span>Team Building</span>
                </div>
                <div class="event-type">
                    <i class="fas fa-baby"></i>
                    <span>Baby Showers</span>
                </div>
                <div class="event-type">
                    <i class="fas fa-tents"></i>
                    <span>Camps</span>
                </div>
                <div class="event-type">
                    <i class="fas fa-heart"></i>
                    <span>Family Reunions</span>
                </div>
                <div class="event-type">
                    <i class="fas fa-ring"></i>
                    <span>Bridal Showers</span>
                </div>
                <div class="event-type">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <span>Workshops & Seminars</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Get In Touch</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>For Booking Call:</h3>
                    <div class="phone-numbers">
                        <a href="tel:0788552706" class="phone-link">
                            <i class="fas fa-phone"></i>
                            0788-552706
                        </a>
                        <a href="tel:0703952076" class="phone-link">
                            <i class="fas fa-phone"></i>
                            0703952076
                        </a>
                    </div>
                    <p class="contact-tagline">"We go where the grill is needed."</p>
                </div>
                <div class="contact-form">
                    <h3>Send us a message</h3>
                    <form id="contact-form">
                        @csrf
                        <div class="form-group">
                            <input type="text" name="name" placeholder="Your Name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" name="email" placeholder="Your Email" required>
                        </div>
                        <div class="form-group">
                            <input type="tel" name="phone" placeholder="Your Phone" required>
                        </div>
                        <div class="form-group">
                            <select name="event_type" required>
                                <option value="">Select Event Type</option>
                                <option value="birthday">Birthday Party</option>
                                <option value="wedding">Wedding</option>
                                <option value="corporate">Corporate Event</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <textarea name="message" placeholder="Tell us about your event..." rows="4" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Send Message</button>
                    </form>
                </div>
            </div>
        </div>
    </section>
@endsection
