<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0 flex items-center">
              <i class="fas fa-fire text-orange-500 text-2xl mr-2"></i>
              <h1 class="text-xl font-bold text-gray-900">Grill Gather Admin</h1>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <button class="text-gray-500 hover:text-gray-700">
              <i class="fas fa-bell"></i>
            </button>
            <div class="relative" ref="dropdown">
              <button
                @click="toggleDropdown"
                class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Admin&background=ff6b35&color=fff" alt="Admin">
                <span class="ml-2 text-gray-700">Admin</span>
                <i class="fas fa-chevron-down ml-1 text-xs"></i>
              </button>

              <!-- Dropdown Menu -->
              <div v-show="showDropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  <i class="fas fa-user mr-2"></i>
                  Profile
                </a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  <i class="fas fa-cog mr-2"></i>
                  Account Settings
                </a>
                <hr class="my-1">
                <button
                  @click="logout"
                  class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <i class="fas fa-sign-out-alt mr-2"></i>
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <div class="flex">
      <!-- Sidebar -->
      <div class="w-64 bg-white shadow-sm min-h-screen">
        <nav class="mt-8 px-4">
          <div class="space-y-2">
            <router-link to="/admin/dashboard" class="sidebar-link" :class="{ active: $route.path === '/admin/dashboard' }">
              <i class="fas fa-tachometer-alt mr-3"></i>
              Dashboard
            </router-link>
            <router-link to="/admin/events" class="sidebar-link" :class="{ active: $route.path.startsWith('/admin/events') }">
              <i class="fas fa-calendar-alt mr-3"></i>
              Events
            </router-link>
            <router-link to="/admin/services" class="sidebar-link" :class="{ active: $route.path.startsWith('/admin/services') }">
              <i class="fas fa-utensils mr-3"></i>
              Services
            </router-link>
            <router-link to="/admin/contacts" class="sidebar-link" :class="{ active: $route.path.startsWith('/admin/contacts') }">
              <i class="fas fa-envelope mr-3"></i>
              Contacts
            </router-link>
            <router-link to="/admin/settings" class="sidebar-link" :class="{ active: $route.path.startsWith('/admin/settings') }">
              <i class="fas fa-cog mr-3"></i>
              Settings
            </router-link>
          </div>
        </nav>
      </div>

      <!-- Main Content -->
      <div class="flex-1 p-8">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {
      showDropdown: false
    }
  },
  mounted() {
    // Load Font Awesome
    if (!document.querySelector('link[href*="font-awesome"]')) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';
      document.head.appendChild(link);
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
    },
    handleClickOutside(event) {
      if (this.$refs.dropdown && !this.$refs.dropdown.contains(event.target)) {
        this.showDropdown = false;
      }
    },
    async logout() {
      try {
        await fetch('/admin/logout', {
          method: 'POST',
          headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json'
          }
        });
        window.location.href = '/admin/login';
      } catch (error) {
        console.error('Logout failed:', error);
      }
    }
  }
}
</script>
