import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '../components/Dashboard.vue'
import Events from '../components/Events.vue'
import EventForm from '../components/EventForm.vue'
import Services from '../components/Services.vue'
import ServiceForm from '../components/ServiceForm.vue'
import Contacts from '../components/Contacts.vue'
import Settings from '../components/Settings.vue'

const routes = [
  {
    path: '/',
    redirect: '/admin/dashboard'
  },
  {
    path: '/admin',
    redirect: '/admin/dashboard'
  },
  {
    path: '/admin/dashboard',
    name: 'Dashboard',
    component: Dashboard
  },
  {
    path: '/admin/events',
    name: 'Events',
    component: Events
  },
  {
    path: '/admin/events/create',
    name: 'CreateEvent',
    component: EventForm
  },
  {
    path: '/admin/events/:id/edit',
    name: 'EditEvent',
    component: EventForm,
    props: true
  },
  {
    path: '/admin/services',
    name: 'Services',
    component: Services
  },
  {
    path: '/admin/services/create',
    name: 'CreateService',
    component: ServiceForm
  },
  {
    path: '/admin/services/:id/edit',
    name: 'EditService',
    component: ServiceForm,
    props: true
  },
  {
    path: '/admin/contacts',
    name: 'Contacts',
    component: Contacts
  },
  {
    path: '/admin/settings',
    name: 'Settings',
    component: Settings
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
