<template>
  <div>
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Services</h1>
        <p class="text-gray-600 mt-2">Manage your catering and event services</p>
      </div>
      <router-link to="/admin/services/create" class="btn-primary">
        <i class="fas fa-plus mr-2"></i>
        Create Service
      </router-link>
    </div>

    <!-- Filters -->
    <div class="card mb-6">
      <div class="flex flex-wrap gap-4">
        <div class="flex-1 min-w-64">
          <input
            v-model="filters.search"
            type="text"
            placeholder="Search services..."
            class="form-input"
            @input="filterServices"
          >
        </div>
        <div>
          <select v-model="filters.status" @change="filterServices" class="form-input">
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
        <div>
          <select v-model="filters.category" @change="filterServices" class="form-input">
            <option value="">All Categories</option>
            <option value="catering">Catering</option>
            <option value="equipment">Equipment</option>
            <option value="venue">Venue</option>
            <option value="entertainment">Entertainment</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Services Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="service in filteredServices" :key="service.id" class="card hover:shadow-lg transition-shadow">
        <div class="flex items-start justify-between mb-4">
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ service.name }}</h3>
            <p class="text-sm text-gray-600 mb-3">{{ truncateText(service.description, 100) }}</p>

            <div class="flex items-center justify-between mb-3">
              <span class="text-lg font-bold text-orange-600">
                UGX {{ formatPrice(service.price) }}
              </span>
              <span class="text-sm text-gray-500">{{ service.category }}</span>
            </div>

            <div class="flex items-center space-x-2 mb-4">
              <span :class="service.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ service.is_active ? 'Active' : 'Inactive' }}
              </span>
              <span v-if="service.is_featured" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Featured
              </span>
            </div>
          </div>
        </div>

        <div class="flex justify-between items-center pt-4 border-t border-gray-200">
          <div class="flex space-x-2">
            <router-link :to="`/admin/services/${service.id}/edit`" class="btn-secondary">
              Edit
            </router-link>
            <button @click="toggleServiceStatus(service)" class="text-blue-600 hover:text-blue-900">
              <i :class="service.is_active ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
            <button @click="deleteService(service)" class="text-red-600 hover:text-red-900">
              <i class="fas fa-trash"></i>
            </button>
          </div>
          <div class="text-xs text-gray-500">
            {{ formatDate(service.created_at) }}
          </div>
        </div>
      </div>
    </div>

    <div v-if="filteredServices.length === 0" class="text-center py-12">
      <i class="fas fa-utensils text-4xl text-gray-400 mb-4"></i>
      <p class="text-gray-500">No services found</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Services',
  data() {
    return {
      services: [],
      filteredServices: [],
      filters: {
        search: '',
        status: '',
        category: ''
      },
      loading: true
    }
  },
  async mounted() {
    await this.loadServices()
  },
  methods: {
    async loadServices() {
      try {
        const response = await this.$http.get('/api/admin/services')

        if (response.data.success) {
          this.services = response.data.data
          this.filteredServices = [...this.services]
        }

        this.loading = false
      } catch (error) {
        console.error('Error loading services:', error)
        this.services = []
        this.filteredServices = []
        this.loading = false
      }
    },
    filterServices() {
      let filtered = [...this.services]

      if (this.filters.search) {
        const search = this.filters.search.toLowerCase()
        filtered = filtered.filter(service =>
          service.name.toLowerCase().includes(search) ||
          service.description.toLowerCase().includes(search) ||
          service.category.toLowerCase().includes(search)
        )
      }

      if (this.filters.status) {
        const isActive = this.filters.status === 'active'
        filtered = filtered.filter(service => service.is_active === isActive)
      }

      if (this.filters.category) {
        filtered = filtered.filter(service => service.category === this.filters.category)
      }

      this.filteredServices = filtered
    },
    async toggleServiceStatus(service) {
      try {
        const response = await this.$http.patch(`/api/admin/services/${service.id}/toggle-status`)

        if (response.data.success) {
          service.is_active = response.data.data.is_active
        }
      } catch (error) {
        console.error('Error updating service status:', error)
      }
    },
    async deleteService(service) {
      if (confirm(`Are you sure you want to delete "${service.name}"?`)) {
        try {
          const response = await this.$http.delete(`/api/admin/services/${service.id}`)

          if (response.data.success) {
            const index = this.services.findIndex(s => s.id === service.id)
            if (index > -1) {
              this.services.splice(index, 1)
              this.filterServices()
            }
          }
        } catch (error) {
          console.error('Error deleting service:', error)
        }
      }
    },
    formatPrice(price) {
      return new Intl.NumberFormat('en-UG').format(price)
    },
    formatDate(date) {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    },
    truncateText(text, length) {
      if (!text) return ''
      return text.length > length ? text.substring(0, length) + '...' : text
    }
  }
}
</script>
