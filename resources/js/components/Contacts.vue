<template>
  <div>
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Contacts</h1>
        <p class="text-gray-600 mt-2">Manage customer inquiries and bookings</p>
      </div>
      <div class="flex space-x-2">
        <button @click="markAllAsRead" class="btn-secondary">
          <i class="fas fa-check-double mr-2"></i>
          Mark All Read
        </button>
        <button @click="exportContacts" class="btn-primary">
          <i class="fas fa-download mr-2"></i>
          Export
        </button>
      </div>
    </div>

    <!-- Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 text-blue-600">
            <i class="fas fa-envelope text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Contacts</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.total }}</p>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
            <i class="fas fa-clock text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">New</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.new }}</p>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 text-green-600">
            <i class="fas fa-phone text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Contacted</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.contacted }}</p>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-gray-100 text-gray-600">
            <i class="fas fa-archive text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Closed</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.closed }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="card mb-6">
      <div class="flex flex-wrap gap-4">
        <div class="flex-1 min-w-64">
          <input
            v-model="filters.search"
            type="text"
            placeholder="Search contacts..."
            class="form-input"
            @input="filterContacts"
          >
        </div>
        <div>
          <select v-model="filters.status" @change="filterContacts" class="form-input">
            <option value="">All Status</option>
            <option value="new">New</option>
            <option value="contacted">Contacted</option>
            <option value="closed">Closed</option>
          </select>
        </div>
        <div>
          <select v-model="filters.eventType" @change="filterContacts" class="form-input">
            <option value="">All Event Types</option>
            <option value="birthday">Birthday Party</option>
            <option value="wedding">Wedding</option>
            <option value="corporate">Corporate Event</option>
            <option value="other">Other</option>
          </select>
        </div>
        <div>
          <input
            v-model="filters.dateFrom"
            type="date"
            class="form-input"
            @change="filterContacts"
          >
        </div>
      </div>
    </div>

    <!-- Contacts List -->
    <div class="space-y-4">
      <div
        v-for="contact in filteredContacts"
        :key="contact.id"
        class="card hover:shadow-md transition-shadow cursor-pointer"
        @click="selectContact(contact)"
        :class="{ 'ring-2 ring-orange-500': selectedContact?.id === contact.id }"
      >
        <div class="flex items-start justify-between">
          <div class="flex items-start space-x-4 flex-1">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                <i class="fas fa-user text-gray-600"></i>
              </div>
            </div>

            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-2 mb-2">
                <h3 class="text-lg font-medium text-gray-900">{{ contact.name }}</h3>
                <span :class="getStatusClass(contact.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ contact.status }}
                </span>
                <span v-if="contact.status === 'new'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  <i class="fas fa-circle text-xs mr-1"></i>
                  Unread
                </span>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                <div class="flex items-center">
                  <i class="fas fa-envelope mr-2"></i>
                  {{ contact.email }}
                </div>
                <div class="flex items-center">
                  <i class="fas fa-phone mr-2"></i>
                  {{ contact.phone }}
                </div>
                <div class="flex items-center">
                  <i class="fas fa-calendar mr-2"></i>
                  {{ contact.event_type }}
                </div>
              </div>

              <p class="text-gray-700 mb-3">{{ truncateText(contact.message, 150) }}</p>

              <div class="flex items-center justify-between text-xs text-gray-500">
                <span>{{ formatDate(contact.created_at) }}</span>
                <span v-if="contact.contacted_at">
                  Contacted: {{ formatDate(contact.contacted_at) }}
                </span>
              </div>
            </div>
          </div>

          <div class="flex-shrink-0 ml-4">
            <div class="flex space-x-2">
              <button
                @click.stop="callContact(contact)"
                class="p-2 text-green-600 hover:bg-green-50 rounded-lg"
                title="Call"
              >
                <i class="fas fa-phone"></i>
              </button>
              <button
                @click.stop="emailContact(contact)"
                class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
                title="Email"
              >
                <i class="fas fa-envelope"></i>
              </button>
              <button
                @click.stop="toggleContactStatus(contact)"
                class="p-2 text-orange-600 hover:bg-orange-50 rounded-lg"
                :title="contact.status === 'new' ? 'Mark as Contacted' : 'Mark as New'"
              >
                <i :class="contact.status === 'new' ? 'fas fa-check' : 'fas fa-undo'"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div v-if="filteredContacts.length === 0" class="card text-center py-12">
        <i class="fas fa-inbox text-6xl text-gray-300 mb-4"></i>
        <h3 class="text-xl font-medium text-gray-900 mb-2">No contacts found</h3>
        <p class="text-gray-500">No contacts match your current filters</p>
      </div>
    </div>

    <!-- Contact Detail Modal -->
    <div v-if="selectedContact" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50" @click="closeModal">
      <div class="bg-white rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto" @click.stop>
        <div class="p-6">
          <div class="flex justify-between items-start mb-6">
            <div>
              <h2 class="text-2xl font-bold text-gray-900">{{ selectedContact.name }}</h2>
              <p class="text-gray-600">{{ selectedContact.event_type }} Inquiry</p>
            </div>
            <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
              <i class="fas fa-times text-xl"></i>
            </button>
          </div>

          <div class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="form-label">Email</label>
                <p class="text-gray-900">{{ selectedContact.email }}</p>
              </div>
              <div>
                <label class="form-label">Phone</label>
                <p class="text-gray-900">{{ selectedContact.phone }}</p>
              </div>
            </div>

            <div>
              <label class="form-label">Message</label>
              <p class="text-gray-900 whitespace-pre-wrap">{{ selectedContact.message }}</p>
            </div>

            <div>
              <label class="form-label">Admin Notes</label>
              <textarea
                v-model="selectedContact.admin_notes"
                rows="3"
                class="form-input"
                placeholder="Add your notes here..."
              ></textarea>
            </div>

            <div class="flex justify-between items-center pt-4 border-t">
              <div class="text-sm text-gray-500">
                Received: {{ formatDate(selectedContact.created_at) }}
              </div>
              <div class="flex space-x-3">
                <button @click="updateContactStatus('contacted')" class="btn-primary">
                  Mark as Contacted
                </button>
                <button @click="updateContactStatus('closed')" class="btn-secondary">
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Contacts',
  data() {
    return {
      contacts: [],
      filteredContacts: [],
      selectedContact: null,
      filters: {
        search: '',
        status: '',
        eventType: '',
        dateFrom: ''
      },
      stats: {
        total: 0,
        new: 0,
        contacted: 0,
        closed: 0
      },
      loading: true
    }
  },
  async mounted() {
    await this.loadContacts()
  },
  methods: {
    async loadContacts() {
      try {
        const response = await this.$http.get('/api/admin/contacts')

        if (response.data.success) {
          this.contacts = response.data.data
          this.updateStats()
          this.filteredContacts = [...this.contacts]
        }

        this.loading = false
      } catch (error) {
        console.error('Error loading contacts:', error)
        this.contacts = []
        this.filteredContacts = []
        this.loading = false
      }
    },
    updateStats() {
      this.stats.total = this.contacts.length
      this.stats.new = this.contacts.filter(c => c.status === 'new').length
      this.stats.contacted = this.contacts.filter(c => c.status === 'contacted').length
      this.stats.closed = this.contacts.filter(c => c.status === 'closed').length
    },
    filterContacts() {
      let filtered = [...this.contacts]

      if (this.filters.search) {
        const search = this.filters.search.toLowerCase()
        filtered = filtered.filter(contact =>
          contact.name.toLowerCase().includes(search) ||
          contact.email.toLowerCase().includes(search) ||
          contact.phone.includes(search) ||
          contact.message.toLowerCase().includes(search)
        )
      }

      if (this.filters.status) {
        filtered = filtered.filter(contact => contact.status === this.filters.status)
      }

      if (this.filters.eventType) {
        filtered = filtered.filter(contact => contact.event_type === this.filters.eventType)
      }

      if (this.filters.dateFrom) {
        filtered = filtered.filter(contact =>
          new Date(contact.created_at) >= new Date(this.filters.dateFrom)
        )
      }

      this.filteredContacts = filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
    },
    selectContact(contact) {
      this.selectedContact = { ...contact }
    },
    closeModal() {
      this.selectedContact = null
    },
    async toggleContactStatus(contact) {
      const newStatus = contact.status === 'new' ? 'contacted' : 'new'
      await this.updateContactStatus(newStatus, contact)
    },
    async updateContactStatus(status, contact = null) {
      const targetContact = contact || this.selectedContact
      if (!targetContact) return

      try {
        const response = await this.$http.patch(`/api/admin/contacts/${targetContact.id}/status`, {
          status: status,
          admin_notes: this.selectedContact?.admin_notes || ''
        })

        if (response.data.success) {
          const index = this.contacts.findIndex(c => c.id === targetContact.id)
          if (index > -1) {
            this.contacts[index].status = status
            if (status === 'contacted') {
              this.contacts[index].contacted_at = new Date().toISOString()
            }
            if (this.selectedContact) {
              this.contacts[index].admin_notes = this.selectedContact.admin_notes
            }
          }

          this.updateStats()
          this.filterContacts()

          if (this.selectedContact) {
            this.closeModal()
          }
        }
      } catch (error) {
        console.error('Error updating contact status:', error)
      }
    },
    async markAllAsRead() {
      try {
        const response = await this.$http.post('/api/admin/contacts/mark-all-read')

        if (response.data.success) {
          this.contacts.forEach(contact => {
            if (contact.status === 'new') {
              contact.status = 'contacted'
              contact.contacted_at = new Date().toISOString()
            }
          })

          this.updateStats()
          this.filterContacts()
        }
      } catch (error) {
        console.error('Error marking all as read:', error)
      }
    },
    callContact(contact) {
      window.open(`tel:${contact.phone}`)
    },
    emailContact(contact) {
      window.open(`mailto:${contact.email}?subject=Re: ${contact.event_type} Event Inquiry`)
    },
    exportContacts() {
      // TODO: Implement export functionality
      console.log('Exporting contacts...')
    },
    getStatusClass(status) {
      const classes = {
        'new': 'bg-blue-100 text-blue-800',
        'contacted': 'bg-green-100 text-green-800',
        'closed': 'bg-gray-100 text-gray-800'
      }
      return classes[status] || 'bg-gray-100 text-gray-800'
    },
    formatDate(date) {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    truncateText(text, length) {
      if (!text) return ''
      return text.length > length ? text.substring(0, length) + '...' : text
    }
  }
}
</script>
