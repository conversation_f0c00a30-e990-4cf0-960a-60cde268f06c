<template>
  <div>
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Events</h1>
        <p class="text-gray-600 mt-2">Manage your events and bookings</p>
      </div>
      <router-link to="/admin/events/create" class="btn-primary">
        <i class="fas fa-plus mr-2"></i>
        Create Event
      </router-link>
    </div>

    <!-- Filters -->
    <div class="card mb-6">
      <div class="flex flex-wrap gap-4">
        <div class="flex-1 min-w-64">
          <input
            v-model="filters.search"
            type="text"
            placeholder="Search events..."
            class="form-input"
            @input="filterEvents"
          >
        </div>
        <div>
          <select v-model="filters.status" @change="filterEvents" class="form-input">
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
        <div>
          <select v-model="filters.featured" @change="filterEvents" class="form-input">
            <option value="">All Events</option>
            <option value="true">Featured Only</option>
            <option value="false">Non-Featured</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Events Table -->
    <div class="card">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Event
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date & Time
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Location
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="event in filteredEvents" :key="event.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-12 w-12">
                    <img
                      v-if="event.image"
                      :src="event.image"
                      :alt="event.title"
                      class="h-12 w-12 rounded-lg object-cover"
                    >
                    <div v-else class="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                      <i class="fas fa-calendar text-orange-600"></i>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ event.title }}</div>
                    <div class="text-sm text-gray-500">{{ truncateText(event.description, 50) }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ formatDate(event.event_date) }}</div>
                <div class="text-sm text-gray-500">{{ event.event_time }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ event.location }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex flex-col space-y-1">
                  <span :class="event.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                    {{ event.is_active ? 'Active' : 'Inactive' }}
                  </span>
                  <span v-if="event.is_featured" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Featured
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <router-link :to="`/admin/events/${event.id}/edit`" class="btn-secondary">
                    Edit
                  </router-link>
                  <button @click="toggleEventStatus(event)" class="text-blue-600 hover:text-blue-900">
                    <i :class="event.is_active ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                  </button>
                  <button @click="toggleFeatured(event)" class="text-yellow-600 hover:text-yellow-900">
                    <i :class="event.is_featured ? 'fas fa-star' : 'far fa-star'"></i>
                  </button>
                  <button @click="deleteEvent(event)" class="text-red-600 hover:text-red-900">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <div v-if="filteredEvents.length === 0" class="text-center py-8 text-gray-500">
          <i class="fas fa-calendar-alt text-4xl mb-4"></i>
          <p>No events found</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Events',
  data() {
    return {
      events: [],
      filteredEvents: [],
      filters: {
        search: '',
        status: '',
        featured: ''
      },
      loading: true
    }
  },
  async mounted() {
    await this.loadEvents()
  },
  methods: {
    async loadEvents() {
      try {
        const response = await this.$http.get('/api/admin/events')

        if (response.data.success) {
          this.events = response.data.data
          this.filteredEvents = [...this.events]
        }

        this.loading = false
      } catch (error) {
        console.error('Error loading events:', error)
        this.events = []
        this.filteredEvents = []
        this.loading = false
      }
    },
    filterEvents() {
      let filtered = [...this.events]

      if (this.filters.search) {
        const search = this.filters.search.toLowerCase()
        filtered = filtered.filter(event =>
          event.title.toLowerCase().includes(search) ||
          event.description.toLowerCase().includes(search) ||
          event.location.toLowerCase().includes(search)
        )
      }

      if (this.filters.status) {
        const isActive = this.filters.status === 'active'
        filtered = filtered.filter(event => event.is_active === isActive)
      }

      if (this.filters.featured) {
        const isFeatured = this.filters.featured === 'true'
        filtered = filtered.filter(event => event.is_featured === isFeatured)
      }

      this.filteredEvents = filtered
    },
    async toggleEventStatus(event) {
      try {
        const response = await this.$http.patch(`/api/admin/events/${event.id}/toggle-status`)

        if (response.data.success) {
          event.is_active = response.data.data.is_active
        }
      } catch (error) {
        console.error('Error updating event status:', error)
      }
    },
    async toggleFeatured(event) {
      try {
        const response = await this.$http.patch(`/api/admin/events/${event.id}/toggle-featured`)

        if (response.data.success) {
          event.is_featured = response.data.data.is_featured
        }
      } catch (error) {
        console.error('Error updating featured status:', error)
      }
    },
    async deleteEvent(event) {
      if (confirm(`Are you sure you want to delete "${event.title}"?`)) {
        try {
          const response = await this.$http.delete(`/api/admin/events/${event.id}`)

          if (response.data.success) {
            const index = this.events.findIndex(e => e.id === event.id)
            if (index > -1) {
              this.events.splice(index, 1)
              this.filterEvents()
            }
          }
        } catch (error) {
          console.error('Error deleting event:', error)
        }
      }
    },
    formatDate(date) {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    },
    truncateText(text, length) {
      if (!text) return ''
      return text.length > length ? text.substring(0, length) + '...' : text
    }
  }
}
</script>
