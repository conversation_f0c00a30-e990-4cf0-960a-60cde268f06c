<template>
  <div>
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
      <p class="text-gray-600 mt-2">Welcome to Grill Gather Admin Panel</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-orange-100 text-orange-600">
            <i class="fas fa-calendar-alt text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Events</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalEvents }}</p>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 text-blue-600">
            <i class="fas fa-utensils text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Active Services</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.activeServices }}</p>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 text-green-600">
            <i class="fas fa-envelope text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">New Contacts</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.newContacts }}</p>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-purple-100 text-purple-600">
            <i class="fas fa-star text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Featured Events</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.featuredEvents }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="card mb-8">
      <h2 class="text-xl font-bold text-gray-900 mb-6">Quick Actions</h2>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <router-link to="/admin/events/create" class="quick-action-btn">
          <i class="fas fa-calendar-plus text-2xl mb-2"></i>
          <span>Add Event</span>
        </router-link>
        <router-link to="/admin/services/create" class="quick-action-btn">
          <i class="fas fa-plus text-2xl mb-2"></i>
          <span>Add Service</span>
        </router-link>
        <router-link to="/admin/contacts" class="quick-action-btn">
          <i class="fas fa-envelope-open text-2xl mb-2"></i>
          <span>View Messages</span>
        </router-link>
        <router-link to="/admin/settings" class="quick-action-btn">
          <i class="fas fa-cog text-2xl mb-2"></i>
          <span>Settings</span>
        </router-link>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Recent Events -->
      <div class="card">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-bold text-gray-900">Recent Events</h2>
          <router-link to="/admin/events" class="text-orange-600 hover:text-orange-700 text-sm font-medium">
            View All
          </router-link>
        </div>
        <div class="space-y-4">
          <div v-for="event in recentEvents" :key="event.id" class="flex items-center p-3 bg-gray-50 rounded-lg">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-calendar text-orange-600"></i>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-sm font-medium text-gray-900">{{ event.title }}</p>
              <p class="text-xs text-gray-500">{{ formatDate(event.event_date) }}</p>
            </div>
            <div class="flex-shrink-0">
              <span :class="event.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ event.is_active ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>
          <div v-if="recentEvents.length === 0" class="text-center py-4 text-gray-500">
            No events found
          </div>
        </div>
      </div>

      <!-- Recent Contacts -->
      <div class="card">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-bold text-gray-900">Recent Contacts</h2>
          <router-link to="/admin/contacts" class="text-orange-600 hover:text-orange-700 text-sm font-medium">
            View All
          </router-link>
        </div>
        <div class="space-y-4">
          <div v-for="contact in recentContacts" :key="contact.id" class="flex items-center p-3 bg-gray-50 rounded-lg">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-user text-green-600"></i>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-sm font-medium text-gray-900">{{ contact.name }}</p>
              <p class="text-xs text-gray-500">{{ contact.event_type }}</p>
            </div>
            <div class="flex-shrink-0">
              <span :class="getStatusClass(contact.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ contact.status }}
              </span>
            </div>
          </div>
          <div v-if="recentContacts.length === 0" class="text-center py-4 text-gray-500">
            No contacts found
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      stats: {
        totalEvents: 0,
        activeServices: 0,
        newContacts: 0,
        featuredEvents: 0
      },
      recentEvents: [],
      recentContacts: [],
      loading: true
    }
  },
  async mounted() {
    await this.loadDashboardData()
  },
  methods: {
    async loadDashboardData() {
      try {
        const response = await this.$http.get('/api/admin/dashboard')

        if (response.data.success) {
          const data = response.data.data
          this.stats = data.stats
          this.recentEvents = data.recentEvents
          this.recentContacts = data.recentContacts
        }

        this.loading = false
      } catch (error) {
        console.error('Error loading dashboard data:', error)
        // Fallback to mock data if API fails
        this.stats = {
          totalEvents: 0,
          activeServices: 0,
          newContacts: 0,
          featuredEvents: 0
        }
        this.recentEvents = []
        this.recentContacts = []
        this.loading = false
      }
    },
    formatDate(date) {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    },
    getStatusClass(status) {
      const classes = {
        'new': 'bg-blue-100 text-blue-800',
        'contacted': 'bg-green-100 text-green-800',
        'closed': 'bg-gray-100 text-gray-800'
      }
      return classes[status] || 'bg-gray-100 text-gray-800'
    }
  }
}
</script>

<style scoped>
.card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 8px;
  text-decoration: none;
  color: #374151;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  min-height: 100px;
}

.quick-action-btn:hover {
  background: #ff6b35;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.quick-action-btn span {
  font-size: 0.9rem;
  font-weight: 500;
}
</style>
