<template>
  <div>
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">
            {{ isEditing ? 'Edit Event' : 'Create Event' }}
          </h1>
          <p class="text-gray-600 mt-2">
            {{ isEditing ? 'Update event details' : 'Add a new event to your calendar' }}
          </p>
        </div>
        <router-link to="/admin/events" class="btn-secondary">
          <i class="fas fa-arrow-left mr-2"></i>
          Back to Events
        </router-link>
      </div>
    </div>

    <div class="card max-w-4xl">
      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- Basic Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="form-label">Event Title *</label>
            <input
              v-model="form.title"
              type="text"
              class="form-input"
              :class="{ 'border-red-500': errors.title }"
              placeholder="Enter event title"
              required
            >
            <p v-if="errors.title" class="text-red-500 text-sm mt-1">{{ errors.title[0] }}</p>
          </div>

          <div>
            <label class="form-label">Location *</label>
            <input
              v-model="form.location"
              type="text"
              class="form-input"
              :class="{ 'border-red-500': errors.location }"
              placeholder="Enter event location"
              required
            >
            <p v-if="errors.location" class="text-red-500 text-sm mt-1">{{ errors.location[0] }}</p>
          </div>
        </div>

        <!-- Date and Time -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="form-label">Event Date *</label>
            <input
              v-model="form.event_date"
              type="date"
              class="form-input"
              :class="{ 'border-red-500': errors.event_date }"
              required
            >
            <p v-if="errors.event_date" class="text-red-500 text-sm mt-1">{{ errors.event_date[0] }}</p>
          </div>

          <div>
            <label class="form-label">Event Time *</label>
            <input
              v-model="form.event_time"
              type="time"
              class="form-input"
              :class="{ 'border-red-500': errors.event_time }"
              required
            >
            <p v-if="errors.event_time" class="text-red-500 text-sm mt-1">{{ errors.event_time[0] }}</p>
          </div>
        </div>

        <!-- Description -->
        <div>
          <label class="form-label">Description</label>
          <textarea
            v-model="form.description"
            rows="4"
            class="form-input"
            :class="{ 'border-red-500': errors.description }"
            placeholder="Enter event description"
          ></textarea>
          <p v-if="errors.description" class="text-red-500 text-sm mt-1">{{ errors.description[0] }}</p>
        </div>

        <!-- Features -->
        <div>
          <label class="form-label">Event Features</label>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <label v-for="feature in availableFeatures" :key="feature.value" class="flex items-center">
              <input
                v-model="form.features"
                :value="feature.value"
                type="checkbox"
                class="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              >
              <span class="ml-2 text-sm text-gray-700">{{ feature.label }}</span>
            </label>
          </div>
        </div>

        <!-- Ticket Prices -->
        <div>
          <label class="form-label">Ticket Prices</label>
          <div class="space-y-3">
            <div v-for="(price, index) in form.ticket_prices" :key="index" class="flex items-center space-x-3">
              <input
                v-model="price.type"
                type="text"
                placeholder="Ticket type (e.g., Adult, Child)"
                class="form-input flex-1"
              >
              <input
                v-model.number="price.price"
                type="number"
                placeholder="Price"
                class="form-input w-32"
                min="0"
              >
              <button
                type="button"
                @click="removeTicketPrice(index)"
                class="text-red-600 hover:text-red-800"
                :disabled="form.ticket_prices.length === 1"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
            <button
              type="button"
              @click="addTicketPrice"
              class="text-orange-600 hover:text-orange-800 text-sm font-medium"
            >
              <i class="fas fa-plus mr-1"></i>
              Add Ticket Price
            </button>
          </div>
        </div>

        <!-- Image Upload -->
        <div>
          <label class="form-label">Event Image</label>
          <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
            <div class="space-y-1 text-center">
              <div v-if="imagePreview" class="mb-4">
                <img :src="imagePreview" alt="Preview" class="mx-auto h-32 w-auto rounded-lg">
              </div>
              <svg v-else class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              <div class="flex text-sm text-gray-600">
                <label class="relative cursor-pointer bg-white rounded-md font-medium text-orange-600 hover:text-orange-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-orange-500">
                  <span>Upload a file</span>
                  <input
                    ref="imageInput"
                    type="file"
                    class="sr-only"
                    accept="image/*"
                    @change="handleImageUpload"
                  >
                </label>
                <p class="pl-1">or drag and drop</p>
              </div>
              <p class="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
            </div>
          </div>
          <p v-if="errors.image" class="text-red-500 text-sm mt-1">{{ errors.image[0] }}</p>
        </div>

        <!-- Status Options -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="flex items-center">
            <input
              v-model="form.is_active"
              type="checkbox"
              class="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
            >
            <label class="ml-2 text-sm text-gray-700">Active (visible to public)</label>
          </div>

          <div class="flex items-center">
            <input
              v-model="form.is_featured"
              type="checkbox"
              class="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
            >
            <label class="ml-2 text-sm text-gray-700">Featured event</label>
          </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <router-link to="/events" class="btn-secondary">
            Cancel
          </router-link>
          <button
            type="submit"
            class="btn-primary"
            :disabled="loading"
          >
            <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
            <i v-else :class="isEditing ? 'fas fa-save' : 'fas fa-plus'" class="mr-2"></i>
            {{ isEditing ? 'Update Event' : 'Create Event' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'EventForm',
  props: {
    id: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      form: {
        title: '',
        description: '',
        event_date: '',
        event_time: '',
        location: '',
        features: [],
        ticket_prices: [{ type: '', price: 0 }],
        is_featured: false,
        is_active: true,
        image: null
      },
      availableFeatures: [
        { value: 'barbecue', label: 'Barbecue' },
        { value: 'live_music', label: 'Live Music' },
        { value: 'games', label: 'Games' },
        { value: 'family_friendly', label: 'Family Friendly' },
        { value: 'drinks', label: 'Drinks' },
        { value: 'dancing', label: 'Dancing' },
        { value: 'outdoor', label: 'Outdoor' },
        { value: 'catering', label: 'Catering' }
      ],
      imagePreview: null,
      loading: false,
      errors: {}
    }
  },
  computed: {
    isEditing() {
      return !!this.id
    }
  },
  async mounted() {
    if (this.isEditing) {
      await this.loadEvent()
    }
  },
  methods: {
    async loadEvent() {
      try {
        this.loading = true
        const response = await axios.get(`/api/admin/events/${this.id}`)

        if (response.data.success) {
          const event = response.data.data
          this.form = {
            title: event.title || '',
            description: event.description || '',
            event_date: event.event_date || '',
            event_time: event.event_time || '',
            location: event.location || '',
            features: event.features || [],
            ticket_prices: event.ticket_prices || [{ type: '', price: 0 }],
            is_featured: event.is_featured || false,
            is_active: event.is_active !== undefined ? event.is_active : true,
            image: null
          }

          if (event.image) {
            this.imagePreview = `/storage/${event.image}`
          }
        }
      } catch (error) {
        console.error('Error loading event:', error)
        this.$router.push('/events')
      } finally {
        this.loading = false
      }
    },
    async submitForm() {
      try {
        this.loading = true
        this.errors = {}

        const formData = new FormData()

        // Add form fields
        Object.keys(this.form).forEach(key => {
          if (key === 'features' || key === 'ticket_prices') {
            formData.append(key, JSON.stringify(this.form[key]))
          } else if (key === 'image' && this.form[key]) {
            formData.append(key, this.form[key])
          } else if (key !== 'image') {
            formData.append(key, this.form[key])
          }
        })

        let response
        if (this.isEditing) {
          formData.append('_method', 'PUT')
          response = await axios.post(`/api/admin/events/${this.id}`, formData, {
            headers: { 'Content-Type': 'multipart/form-data' }
          })
        } else {
          response = await axios.post('/api/admin/events', formData, {
            headers: { 'Content-Type': 'multipart/form-data' }
          })
        }

        if (response.data.success) {
          this.$router.push('/admin/events')
        }
      } catch (error) {
        if (error.response?.status === 422) {
          this.errors = error.response.data.errors || {}
        } else {
          console.error('Error saving event:', error)
        }
      } finally {
        this.loading = false
      }
    },
    handleImageUpload(event) {
      const file = event.target.files[0]
      if (file) {
        this.form.image = file

        // Create preview
        const reader = new FileReader()
        reader.onload = (e) => {
          this.imagePreview = e.target.result
        }
        reader.readAsDataURL(file)
      }
    },
    addTicketPrice() {
      this.form.ticket_prices.push({ type: '', price: 0 })
    },
    removeTicketPrice(index) {
      if (this.form.ticket_prices.length > 1) {
        this.form.ticket_prices.splice(index, 1)
      }
    }
  }
}
</script>
