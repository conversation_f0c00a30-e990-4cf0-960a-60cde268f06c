<template>
  <div class="settings">
    <div class="page-header">
      <h1>Settings</h1>
      <p>Manage your application settings</p>
    </div>

    <div class="settings-sections">
      <!-- General Settings -->
      <div class="settings-card">
        <h2>General Settings</h2>
        <form @submit.prevent="saveGeneralSettings" class="settings-form">
          <div class="form-group">
            <label class="form-label">Site Name</label>
            <input
              v-model="settings.site_name"
              type="text"
              class="form-input"
              placeholder="The Grill Gather"
            >
          </div>

          <div class="form-group">
            <label class="form-label">Site Description</label>
            <textarea
              v-model="settings.site_description"
              class="form-input"
              rows="3"
              placeholder="Your premier BBQ and catering service"
            ></textarea>
          </div>

          <div class="form-group">
            <label class="form-label">Contact Email</label>
            <input
              v-model="settings.contact_email"
              type="email"
              class="form-input"
              placeholder="<EMAIL>"
            >
          </div>

          <div class="form-group">
            <label class="form-label">Contact Phone</label>
            <input
              v-model="settings.contact_phone"
              type="tel"
              class="form-input"
              placeholder="(*************"
            >
          </div>

          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save mr-2"></i>
            Save General Settings
          </button>
        </form>
      </div>

      <!-- Social Media Settings -->
      <div class="settings-card">
        <h2>Social Media</h2>
        <form @submit.prevent="saveSocialSettings" class="settings-form">
          <div class="form-group">
            <label class="form-label">Facebook URL</label>
            <input
              v-model="settings.facebook_url"
              type="url"
              class="form-input"
              placeholder="https://facebook.com/grillgather"
            >
          </div>

          <div class="form-group">
            <label class="form-label">Instagram URL</label>
            <input
              v-model="settings.instagram_url"
              type="url"
              class="form-input"
              placeholder="https://instagram.com/grillgather"
            >
          </div>

          <div class="form-group">
            <label class="form-label">Twitter URL</label>
            <input
              v-model="settings.twitter_url"
              type="url"
              class="form-input"
              placeholder="https://twitter.com/grillgather"
            >
          </div>

          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save mr-2"></i>
            Save Social Settings
          </button>
        </form>
      </div>

      <!-- Business Hours -->
      <div class="settings-card">
        <h2>Business Hours</h2>
        <form @submit.prevent="saveBusinessHours" class="settings-form">
          <div v-for="day in businessHours" :key="day.name" class="business-day">
            <div class="day-header">
              <label class="day-name">{{ day.name }}</label>
              <label class="toggle-switch">
                <input
                  v-model="day.is_open"
                  type="checkbox"
                >
                <span class="slider"></span>
              </label>
            </div>
            <div v-if="day.is_open" class="time-inputs">
              <input
                v-model="day.open_time"
                type="time"
                class="form-input time-input"
              >
              <span class="time-separator">to</span>
              <input
                v-model="day.close_time"
                type="time"
                class="form-input time-input"
              >
            </div>
            <div v-else class="closed-indicator">
              Closed
            </div>
          </div>

          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save mr-2"></i>
            Save Business Hours
          </button>
        </form>
      </div>

      <!-- Email Settings -->
      <div class="settings-card">
        <h2>Email Settings</h2>
        <form @submit.prevent="saveEmailSettings" class="settings-form">
          <div class="form-group">
            <label class="form-label">SMTP Host</label>
            <input
              v-model="emailSettings.smtp_host"
              type="text"
              class="form-input"
              placeholder="smtp.gmail.com"
            >
          </div>

          <div class="form-group">
            <label class="form-label">SMTP Port</label>
            <input
              v-model="emailSettings.smtp_port"
              type="number"
              class="form-input"
              placeholder="587"
            >
          </div>

          <div class="form-group">
            <label class="form-label">SMTP Username</label>
            <input
              v-model="emailSettings.smtp_username"
              type="text"
              class="form-input"
              placeholder="<EMAIL>"
            >
          </div>

          <div class="form-group">
            <label class="form-label">SMTP Password</label>
            <input
              v-model="emailSettings.smtp_password"
              type="password"
              class="form-input"
              placeholder="••••••••"
            >
          </div>

          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save mr-2"></i>
            Save Email Settings
          </button>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Settings',
  data() {
    return {
      settings: {
        site_name: 'The Grill Gather',
        site_description: 'Your premier BBQ and catering service',
        contact_email: '<EMAIL>',
        contact_phone: '(*************',
        facebook_url: '',
        instagram_url: '',
        twitter_url: ''
      },
      businessHours: [
        { name: 'Monday', is_open: true, open_time: '09:00', close_time: '18:00' },
        { name: 'Tuesday', is_open: true, open_time: '09:00', close_time: '18:00' },
        { name: 'Wednesday', is_open: true, open_time: '09:00', close_time: '18:00' },
        { name: 'Thursday', is_open: true, open_time: '09:00', close_time: '18:00' },
        { name: 'Friday', is_open: true, open_time: '09:00', close_time: '20:00' },
        { name: 'Saturday', is_open: true, open_time: '10:00', close_time: '20:00' },
        { name: 'Sunday', is_open: false, open_time: '10:00', close_time: '18:00' }
      ],
      emailSettings: {
        smtp_host: '',
        smtp_port: 587,
        smtp_username: '',
        smtp_password: ''
      }
    }
  },
  mounted() {
    this.loadSettings();
  },
  methods: {
    loadSettings() {
      // Mock data - replace with API call
      console.log('Loading settings...');
    },
    saveGeneralSettings() {
      console.log('Saving general settings:', this.settings);
      // API call to save settings
    },
    saveSocialSettings() {
      console.log('Saving social settings:', this.settings);
      // API call to save social settings
    },
    saveBusinessHours() {
      console.log('Saving business hours:', this.businessHours);
      // API call to save business hours
    },
    saveEmailSettings() {
      console.log('Saving email settings:', this.emailSettings);
      // API call to save email settings
    }
  }
}
</script>

<style scoped>
.settings {
  padding: 2rem;
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

.settings-sections {
  display: grid;
  gap: 2rem;
}

.settings-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.settings-card h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #ff6b35;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.form-input {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: #ff6b35;
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.business-day {
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.day-name {
  font-weight: 500;
  color: #333;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #ff6b35;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.time-inputs {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.time-input {
  width: 120px;
}

.time-separator {
  color: #666;
  font-weight: 500;
}

.closed-indicator {
  color: #666;
  font-style: italic;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.btn-primary {
  background: #ff6b35;
  color: white;
}

.btn-primary:hover {
  background: #e55a2b;
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .settings {
    padding: 1rem;
  }

  .settings-card {
    padding: 1.5rem;
  }

  .time-inputs {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .time-input {
    width: 100%;
  }
}
</style>
