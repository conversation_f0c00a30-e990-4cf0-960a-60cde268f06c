<template>
  <div>
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">
            {{ isEditing ? 'Edit Service' : 'Add Service' }}
          </h1>
          <p class="text-gray-600 mt-2">
            {{ isEditing ? 'Update service details' : 'Add a new service to your offerings' }}
          </p>
        </div>
        <router-link to="/services" class="btn-secondary">
          <i class="fas fa-arrow-left mr-2"></i>
          Back to Services
        </router-link>
      </div>
    </div>

    <div class="card max-w-4xl">
      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- Basic Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="form-label">Service Name *</label>
            <input
              v-model="form.name"
              type="text"
              class="form-input"
              :class="{ 'border-red-500': errors.name }"
              placeholder="Enter service name"
              required
            >
            <p v-if="errors.name" class="text-red-500 text-sm mt-1">{{ errors.name[0] }}</p>
          </div>

          <div>
            <label class="form-label">Category *</label>
            <select
              v-model="form.category"
              class="form-input"
              :class="{ 'border-red-500': errors.category }"
              required
            >
              <option value="">Select category</option>
              <option value="catering">Catering</option>
              <option value="equipment">Equipment</option>
              <option value="venue">Venue</option>
              <option value="entertainment">Entertainment</option>
            </select>
            <p v-if="errors.category" class="text-red-500 text-sm mt-1">{{ errors.category[0] }}</p>
          </div>
        </div>

        <!-- Price and Duration -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="form-label">Price (UGX) *</label>
            <input
              v-model.number="form.price"
              type="number"
              class="form-input"
              :class="{ 'border-red-500': errors.price }"
              placeholder="Enter price"
              min="0"
              required
            >
            <p v-if="errors.price" class="text-red-500 text-sm mt-1">{{ errors.price[0] }}</p>
          </div>

          <div>
            <label class="form-label">Duration</label>
            <input
              v-model="form.duration"
              type="text"
              class="form-input"
              :class="{ 'border-red-500': errors.duration }"
              placeholder="e.g., 2 hours, Full day"
            >
            <p v-if="errors.duration" class="text-red-500 text-sm mt-1">{{ errors.duration[0] }}</p>
          </div>
        </div>

        <!-- Description -->
        <div>
          <label class="form-label">Description *</label>
          <textarea
            v-model="form.description"
            rows="4"
            class="form-input"
            :class="{ 'border-red-500': errors.description }"
            placeholder="Enter service description"
            required
          ></textarea>
          <p v-if="errors.description" class="text-red-500 text-sm mt-1">{{ errors.description[0] }}</p>
        </div>

        <!-- Features -->
        <div>
          <label class="form-label">Service Features</label>
          <div class="space-y-3">
            <div v-for="(feature, index) in form.features" :key="index" class="flex items-center space-x-3">
              <input
                v-model="feature.name"
                type="text"
                placeholder="Feature name"
                class="form-input flex-1"
              >
              <button
                type="button"
                @click="removeFeature(index)"
                class="text-red-600 hover:text-red-800"
                :disabled="form.features.length === 1"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
            <button
              type="button"
              @click="addFeature"
              class="text-orange-600 hover:text-orange-800 text-sm font-medium"
            >
              <i class="fas fa-plus mr-1"></i>
              Add Feature
            </button>
          </div>
        </div>

        <!-- Inclusions -->
        <div>
          <label class="form-label">What's Included</label>
          <div class="space-y-3">
            <div v-for="(inclusion, index) in form.inclusions" :key="index" class="flex items-center space-x-3">
              <input
                v-model="inclusion.item"
                type="text"
                placeholder="Included item"
                class="form-input flex-1"
              >
              <button
                type="button"
                @click="removeInclusion(index)"
                class="text-red-600 hover:text-red-800"
                :disabled="form.inclusions.length === 1"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
            <button
              type="button"
              @click="addInclusion"
              class="text-orange-600 hover:text-orange-800 text-sm font-medium"
            >
              <i class="fas fa-plus mr-1"></i>
              Add Inclusion
            </button>
          </div>
        </div>

        <!-- Image Upload -->
        <div>
          <label class="form-label">Service Image</label>
          <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
            <div class="space-y-1 text-center">
              <div v-if="imagePreview" class="mb-4">
                <img :src="imagePreview" alt="Preview" class="mx-auto h-32 w-auto rounded-lg">
              </div>
              <svg v-else class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              <div class="flex text-sm text-gray-600">
                <label class="relative cursor-pointer bg-white rounded-md font-medium text-orange-600 hover:text-orange-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-orange-500">
                  <span>Upload a file</span>
                  <input
                    ref="imageInput"
                    type="file"
                    class="sr-only"
                    accept="image/*"
                    @change="handleImageUpload"
                  >
                </label>
                <p class="pl-1">or drag and drop</p>
              </div>
              <p class="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
            </div>
          </div>
          <p v-if="errors.image" class="text-red-500 text-sm mt-1">{{ errors.image[0] }}</p>
        </div>

        <!-- Status Options -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="flex items-center">
            <input
              v-model="form.is_active"
              type="checkbox"
              class="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
            >
            <label class="ml-2 text-sm text-gray-700">Active (visible to public)</label>
          </div>

          <div class="flex items-center">
            <input
              v-model="form.is_featured"
              type="checkbox"
              class="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
            >
            <label class="ml-2 text-sm text-gray-700">Featured service</label>
          </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <router-link to="/services" class="btn-secondary">
            Cancel
          </router-link>
          <button
            type="submit"
            class="btn-primary"
            :disabled="loading"
          >
            <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
            <i v-else :class="isEditing ? 'fas fa-save' : 'fas fa-plus'" class="mr-2"></i>
            {{ isEditing ? 'Update Service' : 'Create Service' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ServiceForm',
  props: {
    id: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      form: {
        name: '',
        description: '',
        category: '',
        price: 0,
        duration: '',
        features: [{ name: '' }],
        inclusions: [{ item: '' }],
        is_active: true,
        is_featured: false,
        image: null
      },
      imagePreview: null,
      loading: false,
      errors: {}
    }
  },
  computed: {
    isEditing() {
      return !!this.id
    }
  },
  async mounted() {
    if (this.isEditing) {
      await this.loadService()
    }
  },
  methods: {
    async loadService() {
      try {
        this.loading = true
        const response = await this.$http.get(`/api/admin/services/${this.id}`)

        if (response.data.success) {
          const service = response.data.data
          this.form = {
            name: service.name || '',
            description: service.description || '',
            category: service.category || '',
            price: service.price || 0,
            duration: service.duration || '',
            features: service.features || [{ name: '' }],
            inclusions: service.inclusions || [{ item: '' }],
            is_active: service.is_active !== undefined ? service.is_active : true,
            is_featured: service.is_featured || false,
            image: null
          }

          if (service.image) {
            this.imagePreview = `/storage/${service.image}`
          }
        }
      } catch (error) {
        console.error('Error loading service:', error)
        this.$router.push('/services')
      } finally {
        this.loading = false
      }
    },
    async submitForm() {
      try {
        this.loading = true
        this.errors = {}

        const formData = new FormData()

        // Add form fields
        Object.keys(this.form).forEach(key => {
          if (key === 'features' || key === 'inclusions') {
            formData.append(key, JSON.stringify(this.form[key]))
          } else if (key === 'image' && this.form[key]) {
            formData.append(key, this.form[key])
          } else if (key !== 'image') {
            formData.append(key, this.form[key])
          }
        })

        let response
        if (this.isEditing) {
          formData.append('_method', 'PUT')
          response = await this.$http.post(`/api/admin/services/${this.id}`, formData, {
            headers: { 'Content-Type': 'multipart/form-data' }
          })
        } else {
          response = await this.$http.post('/api/admin/services', formData, {
            headers: { 'Content-Type': 'multipart/form-data' }
          })
        }

        if (response.data.success) {
          this.$router.push('/admin/services')
        }
      } catch (error) {
        if (error.response?.status === 422) {
          this.errors = error.response.data.errors || {}
        } else {
          console.error('Error saving service:', error)
        }
      } finally {
        this.loading = false
      }
    },
    handleImageUpload(event) {
      const file = event.target.files[0]
      if (file) {
        this.form.image = file

        // Create preview
        const reader = new FileReader()
        reader.onload = (e) => {
          this.imagePreview = e.target.result
        }
        reader.readAsDataURL(file)
      }
    },
    addFeature() {
      this.form.features.push({ name: '' })
    },
    removeFeature(index) {
      if (this.form.features.length > 1) {
        this.form.features.splice(index, 1)
      }
    },
    addInclusion() {
      this.form.inclusions.push({ item: '' })
    },
    removeInclusion(index) {
      if (this.form.inclusions.length > 1) {
        this.form.inclusions.splice(index, 1)
      }
    }
  }
}
</script>
