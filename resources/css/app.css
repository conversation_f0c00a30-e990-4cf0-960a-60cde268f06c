@import "tailwindcss";

/* Custom styles for admin panel */
@layer components {
    .btn-primary {
        background-color: #f97316;
        color: white;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: background-color 0.2s;
    }

    .btn-primary:hover {
        background-color: #ea580c;
    }

    .btn-secondary {
        background-color: #6b7280;
        color: white;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: background-color 0.2s;
    }

    .btn-secondary:hover {
        background-color: #4b5563;
    }

    .btn-danger {
        background-color: #ef4444;
        color: white;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        transition: background-color 0.2s;
    }

    .btn-danger:hover {
        background-color: #dc2626;
    }

    .card {
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        padding: 1.5rem;
    }

    .form-input {
        width: 100%;
        padding: 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        outline: none;
        transition: border-color 0.2s, box-shadow 0.2s;
    }

    .form-input:focus {
        border-color: transparent;
        box-shadow: 0 0 0 2px #f97316;
    }

    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .sidebar-link {
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
        color: #374151;
        border-radius: 0.5rem;
        transition: background-color 0.2s, color 0.2s;
    }

    .sidebar-link:hover {
        background-color: #fff7ed;
        color: #ea580c;
    }

    .sidebar-link.active {
        background-color: #fed7aa;
        color: #ea580c;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #f97316;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #ea580c;
}
