/* Copy styles from grill-gather-website/styles.css */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><rect fill="%23654321" width="1200" height="800"/><circle fill="%23ff6b35" cx="200" cy="200" r="50" opacity="0.3"/><circle fill="%23ffd700" cx="800" cy="300" r="30" opacity="0.4"/><circle fill="%23ff6b35" cx="1000" cy="600" r="40" opacity="0.3"/></svg>');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 107, 53, 0.1), rgba(255, 215, 0, 0.1));
}

.hero-content {
    position: relative;
    z-index: 2;
    color: white;
}

.hero-logo {
    position: relative;
    margin-bottom: 2rem;
}

.flame-icon-large {
    font-size: 4rem;
    color: #ff6b35;
    animation: flicker 2s infinite alternate;
}

@keyframes flicker {
    0% { opacity: 1; }
    50% { opacity: 0.8; }
    100% { opacity: 1; }
}

.fork-icon {
    font-size: 3rem;
    color: white;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.grill-highlight {
    color: #ff6b35;
}

.hero-tagline {
    font-size: 1.8rem;
    font-style: italic;
    color: #ffd700;
    margin-bottom: 1rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b35, #ff8c42);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid #ffd700;
}

.btn-secondary:hover {
    background: #ffd700;
    color: #333;
    transform: translateY(-2px);
}

/* Section Titles */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #333;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #ff6b35, #ffd700);
    border-radius: 2px;
}

/* Services Section */
.services {
    padding: 80px 0;
    background: #f8f9fa;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-category {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.service-category:hover {
    transform: translateY(-5px);
}

.service-category h3 {
    color: #ff6b35;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.service-category ul {
    list-style: none;
}

.service-category li {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: space-between;
}

.service-category li i {
    color: #ffd700;
    width: 20px;
    flex-shrink: 0;
}

.service-name {
    flex: 1;
}

.service-price {
    font-weight: 600;
    color: #ff6b35;
    font-size: 0.9rem;
}

/* Events Section */
.events {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.events .section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #333;
    position: relative;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
}

.events .section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #ff6b35, #ffd700);
    border-radius: 2px;
}

.events-slider-container {
    position: relative;
    overflow: hidden;
    margin-top: 2rem;
    width: 100%;
}

.events-slider {
    display: flex;
    gap: 2rem;
    transition: transform 0.3s ease;
    padding: 1rem 2rem;
    width: 100%;
}

.event-card {
    flex: 0 0 400px;
    background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
    border-radius: 20px;
    padding: 2rem;
    color: white;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(255, 107, 53, 0.4);
}

.event-card.featured-event {
    background: linear-gradient(135deg, #ff6b35 0%, #ffd700 100%);
    border: 2px solid #ffd700;
}

.event-header {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.event-date {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 1rem;
    text-align: center;
    min-width: 80px;
    backdrop-filter: blur(10px);
}

.event-date .month {
    display: block;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    opacity: 0.9;
}

.event-date .day {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
    margin: 0.2rem 0;
}

.event-date .year {
    display: block;
    font-size: 0.8rem;
    opacity: 0.8;
}

.event-info {
    flex: 1;
}

.event-info h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.event-time,
.event-location {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.3rem;
    font-size: 0.9rem;
    opacity: 0.9;
}

.event-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.feature {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    backdrop-filter: blur(10px);
}

.event-tickets h4 {
    font-size: 1rem;
    margin-bottom: 0.8rem;
    opacity: 0.9;
}

.ticket-prices {
    display: flex;
    gap: 0.8rem;
    flex-wrap: wrap;
}

.ticket-type {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.8rem 1rem;
    border-radius: 12px;
    text-align: center;
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s ease;
}

.ticket-type:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.ticket-type .price {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.2rem;
}

.ticket-type .type {
    display: block;
    font-size: 0.8rem;
    opacity: 0.9;
    text-transform: capitalize;
}

/* Slider Controls */
.slider-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
}

.slider-btn {
    background: linear-gradient(45deg, #ff6b35, #ff8c42);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.slider-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.slider-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.slider-indicators {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
}

.indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(255, 107, 53, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: #ff6b35;
    transform: scale(1.2);
}

.no-events {
    text-align: center;
    padding: 3rem;
    color: #666;
    max-width: 1200px;
    margin: 0 auto;
}

.no-events p {
    font-size: 1.1rem;
    margin: 0;
}

/* Event Types Section */
.event-types {
    padding: 80px 0;
    background: #f8f9fa;
}

.event-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.event-type {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.event-type:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.event-type i {
    font-size: 2.5rem;
    color: #ff6b35;
    margin-bottom: 1rem;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: linear-gradient(135deg, #333, #555);
    color: white;
}

.contact .section-title {
    color: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h3 {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    color: #ffd700;
}

.phone-numbers {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.phone-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: white;
    text-decoration: none;
    font-size: 1.3rem;
    font-weight: 600;
    padding: 1rem;
    background: rgba(255, 107, 53, 0.2);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.phone-link:hover {
    background: rgba(255, 107, 53, 0.3);
    transform: translateX(5px);
}

.phone-link i {
    color: #ff6b35;
    font-size: 1.5rem;
}

.contact-tagline {
    font-style: italic;
    font-size: 1.2rem;
    color: #ffd700;
    margin-top: 2rem;
}

.contact-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.contact-form h3 {
    margin-bottom: 2rem;
    color: #ffd700;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    background: white;
    box-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-tagline {
        font-size: 1.3rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .event-header {
        flex-direction: column;
        text-align: center;
    }

    .event-features {
        justify-content: center;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .event-types-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .event-card {
        padding: 1.5rem;
    }

    .ticket-prices {
        justify-content: center;
    }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    text-decoration: none;
}

.nav-brand i {
    color: #ff6b35;
    font-size: 2rem;
    animation: flicker 2s infinite alternate;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.nav-links a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 0.5rem 1rem;
}

.nav-links a:hover {
    color: #ff6b35;
}

.admin-link {
    background: linear-gradient(45deg, #ff6b35, #ff8c42);
    border-radius: 25px;
    padding: 0.5rem 1rem !important;
}

.admin-link:hover {
    color: white !important;
    transform: translateY(-2px);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: white;
    transition: 0.3s;
    border-radius: 2px;
}

/* Footer */
.footer {
    background: #222;
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #ffd700;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #ff6b35;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #ff6b35;
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #ff8c42;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #444;
    padding-top: 1rem;
    text-align: center;
    color: #999;
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .nav-toggle {
        display: flex;
    }

    .nav-links {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(0, 0, 0, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        padding: 2rem 0;
        backdrop-filter: blur(10px);
    }

    .nav-links.active {
        left: 0;
    }

    .nav-links a {
        padding: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
}
